import { Template } from 'src/services/api/use-templates-api';
import { AgentFormValues, DEFAULT_FORM_VALUES } from '../config/agent-form-config';

// ----------------------------------------------------------------------

/**
 * Transform template data to form values for editing
 */
export function templateToFormValues(template: Template): AgentFormValues {
  // Extract tool IDs from templateTools array
  const toolsId = template.templateTools?.map((tt) => tt.tool.id) || [];

  return {
    name: template.name,
    description: template.description,
    systemMessage: template.systemMessage,

    type: template.type,
    status: template.status,
    category: template.categoryId.toString(),
    toolsId,
    model: template.model,

    categoryId: template.categoryId,
  };
}

/**
 * Transform form values to API submission format
 */
export function formValuesToSubmission(values: AgentFormValues) {
  return {
    name: values.name,
    description: values.description,
    type: values.type,
    status: values.status,
    categoryId: values.categoryId!,
    // toolsId: values.toolsId,
    systemMessage: values.systemMessage,
    model: values.model,
    // Note: systemPrompt and channels are not part of the API contract
    // They are form-only fields for UI purposes
  };
}

/**
 * Get default form values
 */
export function getDefaultFormValues(): AgentFormValues {
  return { ...DEFAULT_FORM_VALUES } as AgentFormValues;
}

/**
 * Validate if a step can be navigated to
 */
export function canNavigateToStep(
  targetStep: number,
  currentStep: number,
  errors: Record<string, any>
): boolean {
  // Can always navigate backwards
  if (targetStep <= currentStep) {
    return true;
  }

  // For forward navigation, check if all previous steps are valid
  // This would need step field mapping which is in the config
  return Object.keys(errors).length === 0;
}

/**
 * Get step completion status
 */
export function getStepCompletionStatus(
  stepFields: readonly string[],
  errors: Record<string, any>,
  values: Record<string, any>
): { isCompleted: boolean; hasErrors: boolean; isEmpty: boolean } {
  const hasErrors = stepFields.some((field) => errors[field]);
  const isEmpty = stepFields.some((field) => {
    const value = values[field];
    return (
      value === '' ||
      value === null ||
      value === undefined ||
      (Array.isArray(value) && value.length === 0)
    );
  });
  const isCompleted = !hasErrors && !isEmpty;

  return { isCompleted, hasErrors, isEmpty };
}

/**
 * Filter items based on search query
 */
export function filterItemsBySearch<T extends { name: string; description?: string }>(
  items: T[],
  searchQuery: string
): T[] {
  if (!searchQuery.trim()) {
    return items;
  }

  const query = searchQuery.toLowerCase();
  return items.filter(
    (item) =>
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query))
  );
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Generate step navigation breadcrumbs
 */
export function generateStepBreadcrumbs(steps: readonly { label: string }[], activeStep: number) {
  return steps.map((step, index) => ({
    label: step.label,
    isActive: index === activeStep,
    isCompleted: index < activeStep,
    isClickable: index <= activeStep,
  }));
}





/**
 * Format category options for display with search filtering
 */
export function formatCategoryOptions<T extends { id: number; name: string }>(
  categories: T[],
  searchQuery: string
): T[] {
  if (!searchQuery.trim()) {
    return categories;
  }

  const query = searchQuery.toLowerCase();
  return categories.filter((category) =>
    category.name.toLowerCase().includes(query)
  );
}


