import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useToolsApi } from 'src/services/api/use-tools-api';
import { useTemplatesApi } from 'src/services/api/use-templates-api';

// Form validation schema for template (agent)
const agentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  systemMessage: z.string().min(10, 'System message must be at least 10 characters'),
  type: z.enum(['SINGLE', 'MULTI'], {
    required_error: 'Type is required',
  }),
  categoryId: z
    .number({
      required_error: 'Category is required',
    })
    .min(1, 'Category is required'),
  model: z.enum(['GPT_4O_MINI', 'GPT_4O', 'GPT_3_5_TURBO'], {
    required_error: 'Model is required',
  }),
  toolsId: z.array(z.number()).min(1, 'please choose at least one tool').default([]),
});

// Form values type
export type AgentFormValues = z.infer<typeof agentSchema>;

// Agent type (template)
export interface Agent {
  id: number;
  creatorId: number;
  name: string;
  description: string;
  categoryId: number;
  type: 'SINGLE' | 'MULTI';
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  createdAt: string;
  updatedAt: string;
  category: {
    id: number;
    name: string;
    description: string;
    icon: string;
    theme: string;
    createdAt: string;
    updatedAt: string;
  };
}

// Define the available model options as constants
export const MODEL_OPTIONS = [
  { value: 'GPT_4O_MINI', label: 'GPT-4o Mini' },
  { value: 'GPT_4O', label: 'GPT-4o' },
  { value: 'GPT_3_5_TURBO', label: 'GPT-3.5 Turbo' },
] as const;

// Define the available type options as constants
export const TYPE_OPTIONS = [
  { value: 'SINGLE', label: 'Single Agent' },
  { value: 'MULTI', label: 'Multi Agent' },
] as const;

// Steps definition
export const AGENT_FORM_STEPS = [
  {
    label: 'Agent Info',
    icon: 'mdi:account-tie',
    description: 'Enter the basic information about the agent',
  },
  {
    label: 'Agent Tools',
    icon: 'mdi:tools',
    description: 'Select the tools this agent can use',
  },
];

interface UseAgentFormProps {
  agent: Agent | null;
  onSubmit: (data: AgentFormValues) => void;
}

export function useAgentForm({ agent, onSubmit }: UseAgentFormProps) {
  const theme = useTheme();

  // Get categories and tools data from APIs
  const { useGetCategories } = useCategoriesApi();
  const { useGetTools } = useToolsApi();
  const { useCreateTemplate, useUpdateTemplate } = useTemplatesApi();

  const { data: categoriesResponse } = useGetCategories();
  const { data: toolsResponse } = useGetTools();
  const { mutate: createTemplate, isPending } = useCreateTemplate();
  const { mutate: updateTemplate } = useUpdateTemplate(agent?.id || 0);

  const categories = categoriesResponse?.categories || [];
  const tools = toolsResponse?.tools || [];

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // State for search query
  const [searchQuery, setSearchQuery] = useState('');

  // Handle search change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Filter available tools based on search query
  const filteredTools = searchQuery
    ? tools.filter((tool) => tool.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : tools;

  // Initialize form with default values or agent data for editing
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentSchema),
    defaultValues: agent
      ? {
          name: agent.name,
          description: agent.description,
          systemMessage: '', // Not in API response, so default to empty
          type: agent.type,
          categoryId: agent.categoryId,
          model: agent.model,
          toolsId: [], // Not in API response, so default to empty array
        }
      : {
          name: '',
          description: '',
          systemMessage: '',
          type: 'SINGLE' as const,
          categoryId: 0,
          model: 'GPT_4O_MINI' as const,
          toolsId: [],
        },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    watch,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Watch for changes in the tools array
  const selectedTools = watch('toolsId');

  // Handle next step
  const handleNext = async () => {
    let fieldsToValidate: (keyof AgentFormValues)[] = [];

    if (activeStep === 0) {
      fieldsToValidate = ['name', 'description', 'systemMessage', 'categoryId'];
    } else if (activeStep === 1) {
      fieldsToValidate = ['toolsId'];
    }

    const isStepValid = await trigger(fieldsToValidate);
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle tool selection
  const handleToolToggle = (toolId: number) => {
    const currentTools = [...selectedTools];
    const toolIndex = currentTools.indexOf(toolId);

    if (toolIndex === -1) {
      // Add the tool
      currentTools.push(toolId);
    } else {
      // Remove the tool
      currentTools.splice(toolIndex, 1);
    }

    setValue('toolsId', currentTools);
  };

  // Handle form submission
  const onFormSubmit = async (data: AgentFormValues) => {
    if (agent) {
      // Update existing template
      updateTemplate(data, {
        onSuccess: () => {
          onSubmit(data);
          setActiveStep(0);
          reset();
        },
        onError: (error) => {
          console.error('Failed to update template:', error);
        },
      });
    } else {
      // Create new template
      createTemplate(data, {
        onSuccess: () => {
          onSubmit(data);
          setActiveStep(0);
          reset();
        },
        onError: (error) => {
          console.error('Failed to create template:', error);
        },
      });
    }
  };

  const options: { label: string; value: string | number }[] = categories?.map((category) => ({
    label: category.name,
    value: category.id,
  }));

  return {
    theme,
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    handleSubmit,
    searchQuery,
    handleSearchChange,
    availableTools: filteredTools,
    categories,
    tools,
    MODEL_OPTIONS,
    TYPE_OPTIONS,
    isLoading: isPending,
    options,
  };
}
