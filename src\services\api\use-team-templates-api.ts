import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for TemplateTeams
export const TemplateTeamEndpoints = {
  list: '/templates-team',
  details: '/templates-team',
};

// Define the TemplateTeam data type based on API response
export interface TemplateTeam {
  id: number;
  creatorId: number;
  name: string;
  description: string;
  categoryId: number;
  type: 'SINGLE' | 'MULTI';
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  createdAt: string;
  updatedAt: string;
  category: {
    id: number;
    name: string;
    description: string;
    icon: string;
    theme: string;
    createdAt: string;
    updatedAt: string;
  };
}

// Define the API response types
export interface TemplateTeamsListResponse {
  TemplateTeams: TemplateTeam[];
  total: number;
}

// Define the request body types
export interface CreateTemplateTeamRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  toolsId: number[];
}

export interface UpdateTemplateTeamRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  toolsId: number[];
}

// Define query parameters for list endpoint
export interface TemplateTeamsQueryParams {
  take?: number;
  skip?: number;
  name?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// Create a hook to use the TemplateTeams API
export const useTemplateTeamsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all TemplateTeams
  const useGetTemplateTeams = (params?: TemplateTeamsQueryParams) => {
    return apiServices.useGetListService<TemplateTeamsListResponse, TemplateTeamsQueryParams>({
      url: TemplateTeamEndpoints.list,
      params,
    });
  };

  // Get a single TemplateTeam by ID
  const useGetTemplateTeam = (id: number) => {
    return apiServices.useGetItemService<TemplateTeam>({
      url: TemplateTeamEndpoints.details,
      id: id.toString(),
    });
  };

  // Create a new TemplateTeam
  const useCreateTemplateTeam = (onSuccess?: (data: TemplateTeam) => void) => {
    return apiServices.usePostService<CreateTemplateTeamRequest, TemplateTeam>({
      url: TemplateTeamEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a TemplateTeam using PATCH
  const useUpdateTemplateTeam = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<UpdateTemplateTeamRequest>({
      url: TemplateTeamEndpoints.details,
      id: id.toString(),
      onSuccess,
      withFormData: false,
      queryKey: TemplateTeamEndpoints.list + 'list',
    });
  };

  // Delete a TemplateTeam
  const useDeleteTemplateTeam = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: TemplateTeamEndpoints.details,
      urlAfterSuccess: TemplateTeamEndpoints.list + 'list',
      onSuccess,
    });
  };

  return {
    useGetTemplateTeams,
    useGetTemplateTeam,
    useCreateTemplateTeam,
    useUpdateTemplateTeam,
    useDeleteTemplateTeam,
  };
};
