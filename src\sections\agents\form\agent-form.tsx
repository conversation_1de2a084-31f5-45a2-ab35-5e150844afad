import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Stack,
  IconButton,
  <PERSON><PERSON>,
  Step,
  Step<PERSON>abe<PERSON>,
  StepContent,
  Typography,
  Box,
  Grid,
  Radio,
  Card,
  CardActionArea,
  CardContent,
  alpha,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import { Template } from 'src/services/api/use-templates-api';
import { useAgentForm, AgentFormValues } from './use-agent-form';
import ServiceSearchBar from './components/service-search-bar';

// ----------------------------------------------------------------------

// Component props
interface AgentFormProps {
  open: boolean;
  onClose: () => void;
  agent: Template | null;
  onSubmit: (data: AgentFormValues) => void;
}

export default function AgentForm({ open, onClose, agent, onSubmit }: AgentFormProps) {
  // Use the custom hook for form logic
  const {
    theme,
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    handleSubmit,
    searchQuery,
    handleSearchChange,
    availableTools,
    categories,
    tools,
    MODEL_OPTIONS,
    TYPE_OPTIONS,
    isLoading,
    options,
  } = useAgentForm({ agent, onSubmit });

  // Define the step content
  const steps = [
    {
      label: 'Basic Information',
      icon: 'mdi:account-tie',
      description: 'Enter template name, description, and system message',
      fields: (
        <Stack spacing={3}>
          <Field.Text name="name" label="Template Name" placeholder="Enter template name" />
          <Field.Text
            name="description"
            label="Description"
            placeholder="Enter template description"
            multiline
            rows={3}
          />
          <Field.Text
            name="systemMessage"
            label="System Message"
            placeholder="Enter system message for the template"
            multiline
            rows={4}
            helperText="Instructions for the agent's behavior"
          />
          <Field.Select
            name="categoryId"
            label="Select a category"
            placeholder="Select a category"
            options={options}
          />
        </Stack>
      ),
    },
    // {
    //   label: 'Configuration',
    //   icon: 'mdi:cog',
    //   description: 'Select category, type, and model',
    //   fields: (
    //     <Stack spacing={3}>
    //       <Field.Select name="type" label="Type" placeholder="Select template type">
    //         {TYPE_OPTIONS.map((option) => (
    //           <option key={option.value} value={option.value}>
    //             {option.label}
    //           </option>
    //         ))}
    //       </Field.Select>

    //       <Field.Select name="model" label="Model" placeholder="Select AI model">
    //         {MODEL_OPTIONS.map((option) => (
    //           <option key={option.value} value={option.value}>
    //             {option.label}
    //           </option>
    //         ))}
    //       </Field.Select>
    //     </Stack>
    //   ),
    // },
    {
      label: 'Tools Selection',
      icon: 'mdi:tools',
      description: 'Choose tools for this template',
      fields: (
        <Box>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Typography variant="subtitle2">
              Select Tools ({selectedTools.length} selected)
            </Typography>
          </Stack>

          <ServiceSearchBar
            query={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search tools..."
          />

          <Grid container spacing={2}>
            {availableTools.map((tool) => {
              const isSelected = selectedTools.includes(tool.id);
              return (
                <Grid item xs={12} sm={6} md={4} key={tool.id}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: isSelected ? '2px solid' : '1px solid',
                      borderColor: isSelected ? 'primary.main' : 'divider',
                      bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        borderColor: 'primary.main',
                        transform: 'translateY(-2px)',
                        boxShadow: (theme) => theme.shadows[4],
                      },
                    }}
                    onClick={() => handleToolToggle(tool.id)}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Stack spacing={1}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Radio
                            checked={isSelected}
                            onChange={() => handleToolToggle(tool.id)}
                            size="small"
                            sx={{ p: 0 }}
                          />
                          <Typography variant="subtitle2" noWrap>
                            {tool.name}
                          </Typography>
                        </Stack>

                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            minHeight: '2.5em',
                          }}
                        >
                          {tool.description}
                        </Typography>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      ),
    },
  ];

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle sx={{ pb: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          {agent ? 'Edit Template' : 'Create New Template'}
          <IconButton onClick={onClose}>
            <Iconify icon="material-symbols:cancel-outline" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <Form methods={methods} onSubmit={handleSubmit(onFormSubmit)}>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Left side - Vertical Stepper */}
            <Grid item xs={12} md={4}>
              <Stepper activeStep={activeStep} orientation="vertical" connector={null}>
                {steps.map((step, index) => {
                  const isCompleted = index < activeStep;
                  const isActive = index === activeStep;
                  return (
                    <Step key={step.label} completed={isCompleted}>
                      <StepLabel
                        StepIconComponent={() => (
                          <Radio
                            checked={isActive || isCompleted}
                            sx={{
                              p: 0,
                              color: isCompleted ? 'primary.main' : 'inherit',
                            }}
                            readOnly
                          />
                        )}
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify
                            icon={step.icon}
                            width={24}
                            height={24}
                            sx={{
                              color: isActive || isCompleted ? 'primary.main' : 'text.secondary',
                            }}
                          />
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: isActive || isCompleted ? 'primary.main' : 'text.primary',
                              fontWeight: isActive || isCompleted ? 600 : 400,
                            }}
                          >
                            {step.label}
                          </Typography>
                        </Stack>
                      </StepLabel>
                      <StepContent>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, ml: 3 }}>
                          {step.description}
                        </Typography>
                      </StepContent>
                    </Step>
                  );
                })}
              </Stepper>
            </Grid>

            {/* Right side - Form Fields */}
            <Grid item xs={12} md={8}>
              <Box sx={{ p: 1 }}>{steps[activeStep].fields}</Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Stack
            direction="row"
            spacing={2}
            sx={{ width: '100%', justifyContent: 'flex-end', pb: 2, pr: 2 }}
          >
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={onClose}
              fullWidth={false}
            />

            {activeStep > 0 && (
              <AppButton label="Back" variant="outlined" onClick={handleBack} fullWidth={false} />
            )}

            {activeStep < 1 ? (
              <AppButton label="Next" variant="contained" onClick={handleNext} fullWidth={false} />
            ) : (
              <AppButton
                fullWidth={false}
                label={agent ? 'Update Template' : 'Create Template'}
                type="submit"
                variant="contained"
                isLoading={isLoading}
              />
            )}
          </Stack>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
