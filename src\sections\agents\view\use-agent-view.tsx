import { useState, useCallback, useMemo } from 'react';
import { Stack, Typography } from '@mui/material';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { AppTablePropsType } from 'src/components/table';
import { Label } from 'src/components/label';
import LongMenu from 'src/components/long-menu';
import useTable from 'src/components/table/use-table';
import { AgentFormValues } from '../form/use-agent-form';

export const useAgentView = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Filtering states
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');

  const table = useTable({
    defaultOrderBy: 'name',
  });

  // Get templates data from API
  const { useGetTemplates, useDeleteTemplate } = useTemplatesApi();
  const { data: templatesResponse, isLoading, isError, refetch } = useGetTemplates();
  const { mutate: deleteTemplate, isPending } = useDeleteTemplate();

  const templates = templatesResponse?.templates || [];
  const loading = isLoading;
  const error = isError ? 'Failed to load templates' : null;

  // Handle search query change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Handle category filter change
  const handleCategoryChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategoryFilter(event.target.value);
  }, []);

  // Dialog handlers
  const handleOpenDialog = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedTemplate(null);
  }, []);

  const handleOpenCreateDialog = useCallback(() => {
    setSelectedTemplate(null);
    handleOpenDialog();
  }, [handleOpenDialog]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: AgentFormValues) => {
      // Form submission is handled in the form itself
      handleCloseDialog();
      refetch(); // Refetch data after submission
    },
    [handleCloseDialog, refetch]
  );

  // Handle editing a template
  const handleEditTemplate = useCallback(
    (id: number) => {
      const template = templates.find((t) => t.id === id);
      if (template) {
        setSelectedTemplate(template);
        handleOpenDialog();
      }
    },
    [templates, handleOpenDialog]
  );

  // Convert Template to AgentForm format
  const convertTemplateToAgentFormat = useCallback((template: Template | null) => {
    if (!template) return null;

    return {
      id: template.id,
      creatorId: template.creatorId,
      name: template.name,
      description: template.description,
      categoryId: template.categoryId,
      type: template.type,
      model: template.model,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
      category: template.category,
    };
  }, []);

  // Handle opening the confirm dialog for deleting templates
  const handleOpenConfirmDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setSelectedId(null);
  }, []);

  // Handle confirming delete
  const handleConfirmDelete = useCallback(() => {
    if (selectedId) {
      deleteTemplate(selectedId, {
        onSuccess: () => {
          refetch();
          handleCloseConfirmDialog();
        },
        onError: (error) => {
          console.error('Failed to delete template:', error);
          handleCloseConfirmDialog();
        },
      });
    }
  }, [selectedId, deleteTemplate, refetch, handleCloseConfirmDialog]);

  // Filter templates based on search query and category filter
  const filteredTemplates = useMemo(() => {
    return templates.filter((template) => {
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          template.name.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query) ||
          template.category.name.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Filter by category
      if (categoryFilter !== 'all' && template.categoryId.toString() !== categoryFilter) {
        return false;
      }

      return true;
    });
  }, [templates, searchQuery, categoryFilter]);

  // Calculate category options with counts
  const categoryOptions = useMemo(() => {
    const categoryCounts: Record<string, number> = {};

    // Count templates by category
    filteredTemplates.forEach((template) => {
      const categoryId = template.categoryId.toString();
      categoryCounts[categoryId] = (categoryCounts[categoryId] || 0) + 1;
    });

    // Get unique categories from templates
    const uniqueCategories = templates.reduce(
      (acc, template) => {
        const categoryId = template.categoryId.toString();
        if (!acc.find((cat) => cat.value === categoryId)) {
          acc.push({
            value: categoryId,
            label: template.category.name,
            count: categoryCounts[categoryId] || 0,
          });
        }
        return acc;
      },
      [] as { value: string; label: string; count: number }[]
    );

    return [
      { value: 'all', label: 'All Categories', count: templates.length },
      ...uniqueCategories.sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [templates, filteredTemplates]);

  // Menu options for each template row
  const MENU_OPTIONS = useCallback(
    (template: Template) => [
      {
        label: 'Edit',
        icon: 'solar:pen-bold',
        onClick: () => handleEditTemplate(template.id),
      },
      {
        label: 'Delete',
        icon: 'solar:trash-bin-trash-bold',
        onClick: () => handleOpenConfirmDialog(template.id),
        sx: { color: 'error.main' },
      },
    ],
    [handleEditTemplate, handleOpenConfirmDialog]
  );

  // Table columns configuration
  const columns: AppTablePropsType<Template>['columns'] = useMemo(
    () => [
      {
        name: 'name',
        PreviewComponent: (data) => {
          const { name, description } = data;
          return (
            <Stack spacing={0.5} sx={{ maxWidth: 280 }}>
              <Typography variant="subtitle2" noWrap>
                {name}
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.disabled' }} noWrap>
                {description}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'category',
        PreviewComponent: (data) => {
          const { category } = data;
          return (
            <Label
              variant="soft"
              sx={{
                bgcolor: `${category.theme}20`,
                color: category.theme,
                fontWeight: 'medium',
              }}
            >
              {category.name}
            </Label>
          );
        },
      },
      {
        name: 'type',
        PreviewComponent: (data) => {
          const { type } = data;
          return (
            <Label variant="soft" color={type === 'SINGLE' ? 'info' : 'warning'}>
              {type === 'SINGLE' ? 'Single Agent' : 'Multi Agent'}
            </Label>
          );
        },
      },
      {
        name: 'model',
        PreviewComponent: (data) => {
          const { model } = data;
          const modelLabel =
            model === 'GPT_4O_MINI'
              ? 'GPT-4o Mini'
              : model === 'GPT_4O'
                ? 'GPT-4o'
                : 'GPT-3.5 Turbo';
          return (
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {modelLabel}
            </Typography>
          );
        },
      },
      {
        name: 'id',
        cellSx: { width: '50px' },
        PreviewComponent: (row) => {
          return <LongMenu options={MENU_OPTIONS(row)} />;
        },
      },
    ],
    [MENU_OPTIONS]
  );

  return {
    // State
    selectedTemplate,
    openDialog,
    openConfirmDialog,
    selectedId,
    table,
    templates: filteredTemplates,
    loading,
    error,
    isPending,
    searchQuery,
    categoryFilter,
    categoryOptions,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleOpenCreateDialog,
    handleFormSubmit,
    handleEditTemplate,
    convertTemplateToAgentFormat,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    handleSearchChange,
    handleCategoryChange,

    // Table configuration
    MENU_OPTIONS,
    columns,

    // API
    refetch,
  };
};
